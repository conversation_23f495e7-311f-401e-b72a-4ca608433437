#!/usr/bin/env python3
"""
Test script for external ChromaDB configuration.

This script tests the external ChromaDB functionality by:
1. Setting up environment variables for external ChromaDB
2. Creating a VectorStore instance
3. Testing basic operations (create, add, search)

Usage:
    python test_external_chromadb.py

Prerequisites:
    - ChromaDB server running (use docker-compose.chromadb.yml)
    - Required Python packages installed
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "chatbot_support_backend"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_external_chromadb():
    """Test external ChromaDB configuration."""
    
    # Set environment variables for external ChromaDB
    os.environ["USE_EXTERNAL_CHROMA"] = "true"
    os.environ["CHROMA_HOST"] = "localhost"
    os.environ["CHROMA_PORT"] = "8000"
    os.environ["CHROMA_COLLECTION_NAME"] = "test_collection"
    
    try:
        # Import after setting environment variables
        from app.embedding.vector_store import VectorStore, get_chroma_client_settings
        from app.embedding.embedder import Embedder
        from langchain.schema.document import Document
        
        logger.info("Testing external ChromaDB configuration...")
        
        # Test ChromaDB client settings
        settings = get_chroma_client_settings()
        logger.info(f"ChromaDB settings: {settings}")
        
        # Create embedder
        embedder = Embedder(model_name="all-MiniLM-L6-v2")
        logger.info("Created embedder")
        
        # Create vector store
        vector_store = VectorStore(
            embeddings=embedder.embeddings,
            store_type="chroma",
            user_id="test_user"
        )
        logger.info("Created vector store")
        
        # Test documents
        test_docs = [
            Document(
                page_content="This is a test document about machine learning.",
                metadata={"document_id": "test_doc_1", "source": "test"}
            ),
            Document(
                page_content="This document discusses artificial intelligence and neural networks.",
                metadata={"document_id": "test_doc_2", "source": "test"}
            )
        ]
        
        # Create vector store from documents
        vector_store.create_from_documents(test_docs)
        logger.info("Created vector store from test documents")
        
        # Test similarity search
        results = vector_store.similarity_search("machine learning", k=2)
        logger.info(f"Search results: {len(results)} documents found")
        
        for i, doc in enumerate(results):
            logger.info(f"Result {i+1}: {doc.page_content[:50]}...")
        
        logger.info("✅ External ChromaDB test completed successfully!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("Make sure all required packages are installed")
        return False
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        logger.error("Make sure ChromaDB server is running on localhost:8000")
        return False

def test_local_chromadb():
    """Test local ChromaDB configuration for comparison."""
    
    # Set environment variables for local ChromaDB
    os.environ["USE_EXTERNAL_CHROMA"] = "false"
    os.environ["VECTOR_STORE_DIR"] = "/tmp/test_chroma"
    
    try:
        # Import after setting environment variables
        from app.embedding.vector_store import VectorStore, get_chroma_client_settings
        from app.embedding.embedder import Embedder
        from langchain.schema.document import Document
        
        logger.info("Testing local ChromaDB configuration...")
        
        # Test ChromaDB client settings
        settings = get_chroma_client_settings()
        logger.info(f"ChromaDB settings: {settings}")
        
        # Create embedder
        embedder = Embedder(model_name="all-MiniLM-L6-v2")
        logger.info("Created embedder")
        
        # Create vector store
        vector_store = VectorStore(
            embeddings=embedder.embeddings,
            store_type="chroma",
            persist_directory="/tmp/test_chroma",
            user_id="test_user"
        )
        logger.info("Created vector store")
        
        # Test documents
        test_docs = [
            Document(
                page_content="This is a test document about data science.",
                metadata={"document_id": "test_doc_3", "source": "test"}
            )
        ]
        
        # Create vector store from documents
        vector_store.create_from_documents(test_docs)
        logger.info("Created local vector store from test documents")
        
        # Test similarity search
        results = vector_store.similarity_search("data science", k=1)
        logger.info(f"Search results: {len(results)} documents found")
        
        logger.info("✅ Local ChromaDB test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Local test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting ChromaDB configuration tests...")
    
    # Test local ChromaDB first
    logger.info("\n" + "="*50)
    logger.info("Testing LOCAL ChromaDB configuration")
    logger.info("="*50)
    local_success = test_local_chromadb()
    
    # Test external ChromaDB
    logger.info("\n" + "="*50)
    logger.info("Testing EXTERNAL ChromaDB configuration")
    logger.info("="*50)
    logger.info("Note: Make sure ChromaDB server is running:")
    logger.info("  docker-compose -f docker-compose.chromadb.yml up -d")
    external_success = test_external_chromadb()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    logger.info(f"Local ChromaDB:    {'✅ PASS' if local_success else '❌ FAIL'}")
    logger.info(f"External ChromaDB: {'✅ PASS' if external_success else '❌ FAIL'}")
    
    if local_success and external_success:
        logger.info("\n🎉 All tests passed! External ChromaDB is ready for production.")
        sys.exit(0)
    else:
        logger.info("\n⚠️  Some tests failed. Check the logs above for details.")
        sys.exit(1)

#!/usr/bin/env python3
"""
Test script to verify that external ChromaDB configuration doesn't import chromadb library.
This test simulates the external ChromaDB scenario without actually needing a running server.
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "chatbot_support_backend"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_external_chromadb_no_import():
    """Test that external ChromaDB configuration doesn't import chromadb library."""
    
    # Set environment variables for external ChromaDB
    os.environ["USE_EXTERNAL_CHROMA"] = "true"
    os.environ["CHROMA_HOST"] = "localhost"
    os.environ["CHROMA_PORT"] = "8001"
    os.environ["CHROMA_COLLECTION_NAME"] = "test_collection"
    
    try:
        logger.info("Testing external ChromaDB configuration (no import test)...")
        
        # Import the get_chroma_client_settings function
        from app.embedding.vector_store import get_chroma_client_settings
        
        logger.info("✅ Successfully imported vector_store module without chromadb import issues")
        
        # Try to get settings - this should fail with connection error, not import error
        try:
            settings = get_chroma_client_settings()
            logger.info("❌ Unexpected success - should have failed with connection error")
            return False
        except ConnectionError as e:
            logger.info(f"✅ Expected ConnectionError: {e}")
            logger.info("✅ This means chromadb library was NOT imported (no SQLite version check)")
            return True
        except ImportError as e:
            logger.error(f"❌ ImportError (chromadb was imported): {e}")
            return False
        except Exception as e:
            if "sqlite3" in str(e).lower():
                logger.error(f"❌ SQLite error (chromadb was imported): {e}")
                return False
            else:
                logger.info(f"✅ Other error (not SQLite related): {e}")
                return True
        
    except ImportError as e:
        if "chromadb" in str(e):
            logger.error(f"❌ ChromaDB import error: {e}")
            return False
        else:
            logger.error(f"❌ Other import error: {e}")
            return False
    except Exception as e:
        if "sqlite3" in str(e).lower():
            logger.error(f"❌ SQLite error during import: {e}")
            return False
        else:
            logger.error(f"❌ Unexpected error: {e}")
            return False

def test_local_chromadb_import():
    """Test that local ChromaDB configuration works (imports chromadb only when needed)."""
    
    # Set environment variables for local ChromaDB
    os.environ["USE_EXTERNAL_CHROMA"] = "false"
    os.environ["VECTOR_STORE_DIR"] = "/tmp/test_chroma"
    
    try:
        logger.info("Testing local ChromaDB configuration...")
        
        # Import the get_chroma_client_settings function
        from app.embedding.vector_store import get_chroma_client_settings
        
        # Get settings - this should work without importing chromadb
        settings = get_chroma_client_settings()
        logger.info(f"✅ Local ChromaDB settings: {settings}")
        
        if settings.get("is_external") == False:
            logger.info("✅ Correctly identified as local ChromaDB")
            return True
        else:
            logger.error("❌ Incorrectly identified as external ChromaDB")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error with local ChromaDB configuration: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting ChromaDB configuration tests...")
    
    # Test external ChromaDB (should not import chromadb)
    logger.info("\n" + "="*60)
    logger.info("Testing EXTERNAL ChromaDB configuration (no import)")
    logger.info("="*60)
    external_success = test_external_chromadb_no_import()
    
    # Test local ChromaDB
    logger.info("\n" + "="*60)
    logger.info("Testing LOCAL ChromaDB configuration")
    logger.info("="*60)
    local_success = test_local_chromadb_import()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"External ChromaDB (no import): {'✅ PASS' if external_success else '❌ FAIL'}")
    logger.info(f"Local ChromaDB: {'✅ PASS' if local_success else '❌ FAIL'}")
    
    if external_success and local_success:
        logger.info("🎉 All tests passed! The fix works correctly.")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed.")
        sys.exit(1)

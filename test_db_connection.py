#!/usr/bin/env python3
"""
Test script to verify database connection fixes.

This script tests the database connection functionality to ensure it works
correctly in both local and production environments.
"""

import os
import sys
import logging

# Add the chatbot_support_backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'chatbot_support_backend'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_secrets_manager():
    """Test the secrets manager functionality."""
    try:
        logger.info("Testing secrets manager...")
        from app.utils.secrets_manager import get_db_credentials
        
        # Test getting credentials
        creds = get_db_credentials()
        logger.info(f"Successfully retrieved credentials for host: {creds['host']}")
        
        # Validate required fields
        required_fields = ['host', 'port', 'user', 'password', 'database']
        for field in required_fields:
            if field not in creds:
                raise ValueError(f"Missing required field: {field}")
        
        logger.info("✓ Secrets manager test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Secrets manager test failed: {str(e)}")
        return False

def test_database_connection():
    """Test the database connection functionality."""
    try:
        logger.info("Testing database connection...")
        from app.database.db import test_database_connection, get_database_url
        
        # Test URL generation
        db_url = get_database_url()
        logger.info(f"Database URL generated successfully")
        
        # Test connection
        if test_database_connection(max_retries=3):
            logger.info("✓ Database connection test passed")
            return True
        else:
            logger.error("✗ Database connection test failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Database connection test failed: {str(e)}")
        return False

def test_database_initialization():
    """Test the database initialization functionality."""
    try:
        logger.info("Testing database initialization...")
        from app.database.db import wait_for_database
        
        # Test waiting for database
        if wait_for_database(max_wait_time=60, check_interval=2):
            logger.info("✓ Database initialization test passed")
            return True
        else:
            logger.error("✗ Database initialization test failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Database initialization test failed: {str(e)}")
        return False

def main():
    """Run all database connection tests."""
    logger.info("Starting database connection tests...")
    
    # Print environment info
    env = os.getenv('ENV', 'local')
    logger.info(f"Environment: {env}")
    
    if env == 'production':
        db_secret_arn = os.getenv('DB_SECRET_ARN')
        logger.info(f"DB Secret ARN: {db_secret_arn}")
    else:
        db_host = os.getenv('DB_HOST', 'db')
        logger.info(f"DB Host: {db_host}")
    
    # Run tests
    tests = [
        ("Secrets Manager", test_secrets_manager),
        ("Database Connection", test_database_connection),
        ("Database Initialization", test_database_initialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        if test_func():
            passed += 1
        else:
            logger.error(f"{test_name} test failed!")
    
    # Summary
    logger.info(f"\n--- Test Summary ---")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("✓ All tests passed!")
        return 0
    else:
        logger.error(f"✗ {total - passed} test(s) failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())

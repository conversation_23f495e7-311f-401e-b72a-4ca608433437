FROM public.ecr.aws/docker/library/python:3.10.13-slim-bullseye
# FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    poppler-utils \
    libpoppler-cpp-dev \
    pkg-config \
    curl \
    # Dependencies for PyMuPDF (for PDF image extraction)
    libmupdf-dev \
    mupdf-tools \
    libfreetype6-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install additional dependencies for production
RUN pip install --no-cache-dir \
    gunicorn \
    uvicorn[standard] \
    boto3 \
    pymysql \
    cryptography \
    python-jose[cryptography] \
    passlib[bcrypt] \
    sqlalchemy

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . /app/

# Expose port
EXPOSE 8000

# Command to run the application
CMD ["gunicorn", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000", "--workers", "4", "--timeout", "120", "app.api.app:app"]

"""
Database migration script to add tables for dropdown options.

This script creates tables for storing ServiceName, SoftwareMenu, and IssueType options.
"""

import logging
import os
import sys
import uuid
from sqlalchemy import text

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from app.database.db import get_db, engine
from app.database.models import Base, ServiceNameOption, SoftwareMenuOption, IssueTypeOption

logger = logging.getLogger(__name__)

def run_migration():
    """
    Run the migration to create dropdown options tables.
    """
    try:
        # Create the tables if they don't exist
        Base.metadata.create_all(engine)

        # Insert default values for each dropdown
        with get_db() as db:
            # Check if service name options exist
            service_name_count = db.query(ServiceNameOption).count()
            if service_name_count == 0:
                logger.info("Adding default service name options...")
                default_service_names = [
                    "Customer Support",
                    "Technical Support",
                    "Sales",
                    "Billing",
                    "Other"
                ]
                for value in default_service_names:
                    db.add(ServiceNameOption(id=str(uuid.uuid4()), value=value))
                db.commit()
                logger.info(f"Added {len(default_service_names)} default service name options")

            # Check if software menu options exist
            software_menu_count = db.query(SoftwareMenuOption).count()
            if software_menu_count == 0:
                logger.info("Adding default software menu options...")
                default_software_menus = [
                    "Dashboard",
                    "Reports",
                    "Settings",
                    "User Management",
                    "Other"
                ]
                for value in default_software_menus:
                    db.add(SoftwareMenuOption(id=str(uuid.uuid4()), value=value))
                db.commit()
                logger.info(f"Added {len(default_software_menus)} default software menu options")

            # Check if issue type options exist
            issue_type_count = db.query(IssueTypeOption).count()
            if issue_type_count == 0:
                logger.info("Adding default issue type options...")
                default_issue_types = [
                    "Bug",
                    "Feature Request",
                    "Question",
                    "Documentation",
                    "Other"
                ]
                for value in default_issue_types:
                    db.add(IssueTypeOption(id=str(uuid.uuid4()), value=value))
                db.commit()
                logger.info(f"Added {len(default_issue_types)} default issue type options")

        logger.info("Successfully created dropdown options tables and added default values")

    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the migration
    run_migration()

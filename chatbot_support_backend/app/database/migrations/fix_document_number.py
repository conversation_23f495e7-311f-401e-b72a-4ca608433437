"""
Database migration script to ensure document_number column exists in documents table.
"""

import logging
import os
import sys
from sqlalchemy import text

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from app.database.db import get_db, engine
from app.database.models import Document
from app.utils.document_number import get_unique_document_number

logger = logging.getLogger(__name__)

def run_migration():
    """
    Run the migration to ensure document_number column exists in documents table.
    """
    try:
        # Check if the column already exists
        with engine.connect() as connection:
            # Check if the column exists in MySQL
            result = connection.execute(text("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'ragchatbot' AND table_name = 'documents' AND column_name = 'document_number'"))
            column_exists = result.scalar() > 0
            
            if not column_exists:
                # Add the document_number column
                logger.info("Adding document_number column to documents table...")
                connection.execute(text("ALTER TABLE documents ADD COLUMN document_number VARCHAR(20) UNIQUE"))
                connection.commit()
                logger.info("Successfully added document_number column to documents table")
            else:
                logger.info("document_number column already exists in documents table")
                
        # Generate document numbers for existing documents that don't have one
        with get_db() as db:
            documents = db.query(Document).filter(Document.document_number == None).all()
            
            if documents:
                logger.info(f"Found {len(documents)} documents without document numbers")
                
                for document in documents:
                    document_number = get_unique_document_number(db, prefix="DOC")
                    document.document_number = document_number
                    logger.info(f"Assigned document number {document_number} to document {document.id}")
                
                db.commit()
                logger.info("Successfully assigned document numbers to all documents")
            else:
                logger.info("All documents already have document numbers")
        
    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the migration
    run_migration()

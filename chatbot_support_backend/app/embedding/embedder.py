"""
Document embedding generation and management using Google Vertex AI.
"""

import os
import logging
from typing import List, Optional

from langchain_google_vertexai import VertexAIEmbeddings

logger = logging.getLogger(__name__)

class DocumentEmbedder:
    """
    Handles the generation of embeddings for document chunks using Google Vertex AI.
    """

    def __init__(
        self,
        embedding_model: str = "text-embedding-004",
        google_service_account_file: Optional[str] = None,
        google_project_id: Optional[str] = None,
        **kwargs  # Accept additional parameters for backward compatibility
    ):
        """
        Initialize the document embedder with Google Vertex AI.

        Args:
            embedding_model: The name of the Vertex AI embedding model to use
            google_service_account_file: Path to Google service account JSON file
            google_project_id: Google Cloud project ID
            **kwargs: Additional parameters for backward compatibility (ignored)
        """
        self.embedding_model = embedding_model
        self.google_project_id = google_project_id

        # Initialize Google Vertex AI embeddings
        self._initialize_google_embeddings(
            google_service_account_file=google_service_account_file,
            google_project_id=google_project_id
        )

    def _initialize_google_embeddings(
        self,
        google_service_account_file: Optional[str] = None,
        google_project_id: Optional[str] = None
    ):
        """
        Initialize Google Vertex AI embeddings.

        Args:
            google_service_account_file: Path to Google service account JSON file
            google_project_id: Google Cloud project ID
        """
        try:
            # Set up authentication
            if google_service_account_file and os.path.exists(google_service_account_file):
                os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = google_service_account_file
                logger.info(f"Using Google service account file: {google_service_account_file}")
            elif not os.environ.get("GOOGLE_APPLICATION_CREDENTIALS"):
                logger.warning("No Google service account file found. Using default credentials.")

            # Map embedding model names to Vertex AI model names
            vertex_model_map = {
                # New models (more widely available)
                "text-embedding-004": "text-embedding-004",
                "text-embedding-preview-0815": "text-embedding-preview-0815",
                "textembedding-gecko@004": "textembedding-gecko@004",

                # Legacy models (may not be available in all regions)
                "textembedding-gecko@001": "text-embedding-004",  # Fallback to newer model
                "textembedding-gecko@003": "text-embedding-004",  # Fallback to newer model
                "textembedding-gecko": "text-embedding-004",      # Fallback to newer model
                "all-MiniLM-L6-v2": "text-embedding-004",         # Fallback for legacy model names
            }

            # Get the Vertex AI model name
            vertex_model = vertex_model_map.get(self.embedding_model, "text-embedding-004")

            # Initialize Vertex AI embeddings
            self.embeddings = VertexAIEmbeddings(
                model_name=vertex_model,
                project=google_project_id
            )

            logger.info(f"Using Google Vertex AI embeddings with model: {vertex_model} (project: {google_project_id})")

        except Exception as e:
            logger.error(f"Error initializing Google Vertex AI embeddings: {str(e)}")
            raise ValueError(f"Error initializing Google Vertex AI embeddings: {str(e)}")

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for a list of texts using Google Vertex AI.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors
        """
        try:
            embeddings = self.embeddings.embed_documents(texts)
            logger.info(f"Generated {len(embeddings)} embeddings using Google Vertex AI")
            return embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise

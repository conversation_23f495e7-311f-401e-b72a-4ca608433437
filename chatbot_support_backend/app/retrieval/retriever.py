"""
Document retrieval functionality for finding relevant context.
"""

import logging
from typing import List, Dict, Any, Optional, Union

from langchain.schema.document import Document
from langchain.schema.retriever import BaseRetriever
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor
from langchain_openai import ChatOpenAI

from app.embedding.vector_store import VectorStore

logger = logging.getLogger(__name__)

class DocumentRetriever:
    """
    Handles the retrieval of relevant document chunks for user queries.
    """

    def __init__(
        self,
        vector_store: VectorStore,
        use_compression: bool = False,
        llm_model_name: str = "gpt-3.5-turbo",
        top_k: int = 4,
        user_id: Optional[str] = None
    ):
        """
        Initialize the document retriever.

        Args:
            vector_store: The vector store to retrieve documents from
            use_compression: Whether to use contextual compression
            llm_model_name: The LLM model to use for compression
            top_k: Number of documents to retrieve
            user_id: Optional user ID for access control
        """
        self.vector_store = vector_store
        self.use_compression = use_compression
        self.top_k = top_k
        self.user_id = user_id

        # Create the base retriever if vector store is initialized
        if self.vector_store.vector_store is not None:
            self.base_retriever = self.vector_store.vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={"k": top_k}
            )
        else:
            self.base_retriever = None
            logger.warning("Vector store not initialized. Retriever will be initialized when documents are added.")

        # Set up retriever if base_retriever is available
        if self.base_retriever is not None:
            # Set up compression if requested
            if use_compression:
                try:
                    from langchain_openai import ChatOpenAI
                    from langchain.retrievers import ContextualCompressionRetriever
                    from langchain.retrievers.document_compressors import LLMChainExtractor

                    llm = ChatOpenAI(model_name=llm_model_name, temperature=0)
                    compressor = LLMChainExtractor.from_llm(llm)
                    self.retriever = ContextualCompressionRetriever(
                        base_compressor=compressor,
                        base_retriever=self.base_retriever
                    )
                    logger.info(f"Using contextual compression with model: {llm_model_name}")
                except ImportError:
                    # Fall back to standard retrieval if imports fail
                    self.retriever = self.base_retriever
                    logger.warning("Failed to import compression modules, falling back to standard retrieval")
            else:
                self.retriever = self.base_retriever
                logger.info("Using standard retrieval without compression")
        else:
            self.retriever = None
            logger.warning("Retriever not initialized. Will return empty results until documents are added.")

    def retrieve(self, query: str, user_id: Optional[str] = None) -> List[Document]:
        """
        Retrieve relevant documents for a query.

        Args:
            query: The user query
            user_id: Optional user ID for access control (overrides the one set in constructor)

        Returns:
            List of relevant Document objects
        """
        try:
            # Check if retriever is initialized
            if self.retriever is None:
                # Try to initialize the retriever if vector store is available
                if self.vector_store and self.vector_store.vector_store is not None:
                    try:
                        # Create the base retriever
                        self.base_retriever = self.vector_store.vector_store.as_retriever(
                            search_type="similarity",
                            search_kwargs={"k": self.top_k}
                        )

                        # Set up compression if requested
                        if self.use_compression:
                            try:
                                from langchain_openai import ChatOpenAI
                                from langchain.retrievers import ContextualCompressionRetriever
                                from langchain.retrievers.document_compressors import LLMChainExtractor

                                llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0)
                                compressor = LLMChainExtractor.from_llm(llm)
                                self.retriever = ContextualCompressionRetriever(
                                    base_compressor=compressor,
                                    base_retriever=self.base_retriever
                                )
                                logger.info("Initialized retriever with contextual compression")
                            except ImportError:
                                # Fall back to standard retrieval if imports fail
                                self.retriever = self.base_retriever
                                logger.warning("Failed to import compression modules, falling back to standard retrieval")
                        else:
                            self.retriever = self.base_retriever
                            logger.info("Initialized standard retriever without compression")
                    except Exception as init_error:
                        logger.error(f"Error initializing retriever: {str(init_error)}")
                        self.retriever = None

                # If retriever is still None, return empty results
                if self.retriever is None:
                    logger.warning(f"Retriever not initialized, returning empty results for query: {query}")
                    return []

            # Use the user_id from parameter or from constructor
            effective_user_id = user_id or self.user_id

            if effective_user_id and self.vector_store.store_type == "chroma":
                # For Chroma with access control, use the vector store's similarity_search with access control
                documents = self.vector_store.similarity_search(
                    query=query,
                    k=self.top_k,
                    user_id=effective_user_id
                )
                logger.info(f"Retrieved {len(documents)} documents for user {effective_user_id} and query: {query}")
                return documents
            else:
                # Use the standard retriever without access control
                documents = self.retriever.get_relevant_documents(query)
                logger.info(f"Retrieved {len(documents)} documents for query: {query}")
                return documents
        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}")
            # Return empty list instead of raising an exception
            return []

    def retrieve_with_scores(self, query: str, user_id: Optional[str] = None) -> List[tuple]:
        """
        Retrieve relevant documents with similarity scores.

        Args:
            query: The user query
            user_id: Optional user ID for access control (overrides the one set in constructor)

        Returns:
            List of (Document, score) tuples
        """
        try:
            # Check if vector store is initialized
            if self.vector_store.vector_store is None:
                logger.warning(f"Vector store not initialized, returning empty results for query: {query}")
                return []

            # Use the user_id from parameter or from constructor
            effective_user_id = user_id or self.user_id

            # Get documents the user has access to if user_id is provided
            accessible_docs = None
            if effective_user_id:
                from app.auth.user import UserManager
                user_manager = UserManager()
                accessible_docs = user_manager.get_accessible_documents(effective_user_id)
                logger.info(f"User {effective_user_id} has access to {len(accessible_docs)} documents")

            # This only works with the base retriever, not the compressed one
            if self.use_compression:
                logger.warning("Scores not available when using compression, falling back to standard retrieval")

                # Get more results than needed to account for filtering
                documents_with_scores = self.vector_store.vector_store.similarity_search_with_score(
                    query, k=self.top_k * 3 if accessible_docs else self.top_k
                )
            else:
                # Get more results than needed to account for filtering
                documents_with_scores = self.vector_store.vector_store.similarity_search_with_score(
                    query, k=self.top_k * 3 if accessible_docs else self.top_k
                )

            # Filter results based on document access if needed
            if accessible_docs:
                documents_with_scores = [
                    (doc, score) for doc, score in documents_with_scores
                    if doc.metadata.get('document_id') in accessible_docs
                ]
                logger.info(f"Filtered to {len(documents_with_scores)} results based on document access")
                # Limit to top_k results
                documents_with_scores = documents_with_scores[:self.top_k]

            logger.info(f"Retrieved {len(documents_with_scores)} documents with scores for query: {query}")
            return documents_with_scores
        except Exception as e:
            logger.error(f"Error retrieving documents with scores: {str(e)}")
            # Return empty list instead of raising an exception
            return []

"""
Chatbot storage module for the RAG chatbot.
"""

import logging
from typing import Op<PERSON>, Dict, Any

from ..database.db import get_db
from ..database.models import ChatbotConfig
from ..chatbot import RAGChatbot

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatbotStorage:
    """
    Manages the storage and retrieval of chatbot instances.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ChatbotStorage, cls).__new__(cls)
            cls._instance.chatbots = {}
        return cls._instance

    def get(self, user_id: str) -> Optional[RAGChatbot]:
        """
        Get a chatbot instance for a user.

        Args:
            user_id: The ID of the user

        Returns:
            The chatbot instance if found, None otherwise
        """
        # Check if the chatbot is already in memory
        if user_id in self.chatbots:
            logger.info(f"Found chatbot in memory for user {user_id}")
            return self.chatbots[user_id]

        # If not, try to load it from the database
        logger.info(f"Chatbot not found in memory for user {user_id}, trying to load from database")
        with get_db() as db:
            config = db.query(ChatbotConfig).filter(ChatbotConfig.user_id == user_id).first()

            if config:
                logger.info(f"Found chatbot config in database for user {user_id}")
                # Create a new chatbot instance from the config
                chatbot = RAGChatbot.load(
                    vector_store_path=config.vector_store_dir,
                    use_google_llm=config.use_google_llm,
                    google_service_account_file=config.google_service_account_file,
                    google_project_id=config.google_project_id,
                    embedding_model=config.embedding_model,
                    llm_model_name=config.llm_model_name,
                    vector_store_type=config.vector_store_type,
                    use_compression=config.use_compression,
                    top_k=config.top_k,
                    max_memory_messages=config.max_memory_messages,
                    user_id=user_id
                )

                # Store the chatbot in memory
                self.chatbots[user_id] = chatbot
                return chatbot

        logger.info(f"No chatbot config found in database for user {user_id}")
        return None

    def set(self, user_id: str, chatbot: RAGChatbot, config: Dict[str, Any]) -> None:
        """
        Set a chatbot instance for a user.

        Args:
            user_id: The ID of the user
            chatbot: The chatbot instance
            config: The chatbot configuration
        """
        # Store the chatbot in memory
        self.chatbots[user_id] = chatbot

        # Store the config in the database
        with get_db() as db:
            # Check if a config already exists for this user
            existing_config = db.query(ChatbotConfig).filter(ChatbotConfig.user_id == user_id).first()

            if existing_config:
                # Update the existing config
                for key, value in config.items():
                    if hasattr(existing_config, key):
                        setattr(existing_config, key, value)
                logger.info(f"Updated chatbot config in database for user {user_id}")
            else:
                # Create a new config
                new_config = ChatbotConfig(
                    user_id=user_id,
                    use_openai_embeddings=False,  # No longer used
                    use_google_embeddings=True,   # Always use Google embeddings now
                    use_google_llm=config.get("use_google_llm", False),
                    openai_api_key=None,  # No longer used
                    google_api_key=None,  # No longer used
                    google_service_account_file=config.get("google_service_account_file"),
                    google_project_id=config.get("google_project_id"),
                    embedding_model=config.get("embedding_model", "text-embedding-004"),
                    llm_model_name=config.get("llm_model_name", "gpt-3.5-turbo"),
                    vector_store_type=config.get("vector_store_type", "chroma"),
                    vector_store_dir=config.get("vector_store_dir"),
                    use_compression=config.get("use_compression", False),
                    top_k=config.get("top_k", 4),
                    max_memory_messages=config.get("max_memory_messages", 10)
                )
                db.add(new_config)
                logger.info(f"Created new chatbot config in database for user {user_id}")

            db.commit()

    def delete(self, user_id: str) -> None:
        """
        Delete a chatbot instance for a user.

        Args:
            user_id: The ID of the user
        """
        # Remove the chatbot from memory
        if user_id in self.chatbots:
            del self.chatbots[user_id]

        # Remove the config from the database
        with get_db() as db:
            db.query(ChatbotConfig).filter(ChatbotConfig.user_id == user_id).delete()
            db.commit()
            logger.info(f"Deleted chatbot config from database for user {user_id}")

    def list(self) -> list:
        """
        List all user IDs with chatbot instances.

        Returns:
            List of user IDs
        """
        return list(self.chatbots.keys())

    def update(self, user_id: str, chatbot: RAGChatbot) -> None:
        """
        Update a chatbot instance for a user without changing the configuration.

        This is useful when you want to update the chatbot's state (e.g., vector store)
        without changing its configuration.

        Args:
            user_id: The ID of the user
            chatbot: The updated chatbot instance
        """
        # Update the chatbot in memory
        self.chatbots[user_id] = chatbot
        logger.info(f"Updated chatbot in memory for user {user_id}")

    def clear_all(self) -> None:
        """
        Clear all chatbot instances from memory.

        This does not delete the configurations from the database.
        Use this when you want to force all chatbots to be reinitialized.
        """
        self.chatbots = {}
        logger.info("Cleared all chatbot instances from memory")

    def get_all_chatbots(self) -> Dict[str, RAGChatbot]:
        """
        Get all chatbot instances currently in memory.

        Returns:
            Dictionary mapping user IDs to chatbot instances
        """
        logger.info(f"Returning {len(self.chatbots)} chatbot instances from memory")
        return self.chatbots

    def remove(self, user_id: str) -> None:
        """
        Remove a chatbot instance from memory only (not from database).

        Args:
            user_id: The user ID
        """
        if user_id in self.chatbots:
            del self.chatbots[user_id]
            logger.info(f"Removed chatbot for user {user_id} from memory")
        else:
            logger.info(f"No chatbot found in memory for user {user_id}")



"""
Document URL generation module.

This module provides functions to generate presigned URLs for documents stored in S3.
"""

import os
import logging
from typing import Dict, Any, Optional

from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.database.db import get_db
from app.database.models import Document as DBDocument
from app.storage.s3 import S3Storage
from app.auth.user import UserManager

# Set up logging
logger = logging.getLogger(__name__)

def generate_document_url(
    document_id: str,
    user_id: str,
    user_manager: UserManager,
    expiration: int = 3600,
    page: Optional[int] = None
) -> Dict[str, Any]:
    """
    Generate a presigned URL for a document.

    Args:
        document_id: The ID of the document
        user_id: The ID of the user requesting the URL
        user_manager: UserManager instance for access control
        expiration: URL expiration time in seconds (default: 1 hour)
        page: Optional page number to add as a fragment to the URL

    Returns:
        Dictionary with document details and URL

    Raises:
        HTTPException: If document not found or user doesn't have access
    """
    try:
        # Check if document exists
        with get_db() as db:
            document = db.query(DBDocument).filter(DBDocument.id == document_id).first()

            if not document:
                logger.warning(f"Document with ID {document_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document with ID {document_id} not found"
                )

            # Check if user has access to the document
            is_admin = user_manager.is_admin(user_id)
            if not is_admin and not user_manager.user_has_document_access(user_id, document_id):
                logger.warning(f"User {user_id} does not have access to document {document_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have access to this document"
                )

            # Get document details
            s3_key = document.s3_key
            filename = document.filename

            # Initialize S3 storage with explicit AWS credentials from environment
            s3_storage = S3Storage(
                bucket_name=os.getenv("S3_BUCKET_NAME", "jmscpos-sandbox-chatbot-poc-bucket"),
                prefix=os.getenv("S3_PREFIX", "chatbot_support")
            )

            # Generate presigned URL with detailed logging
            try:
                # Log document metadata for debugging
                logger.info(f"Document metadata: id={document_id}, filename={filename}, s3_key={s3_key}")

                # Generate the URL with KMS support
                url = s3_storage.get_file_url(s3_key, expiration=expiration)
                logger.info(f"Generated presigned URL for document {document_id} with S3 key {s3_key}")

                # Verify the URL is valid
                if not url or not url.startswith('http'):
                    logger.warning(f"Generated URL may not be valid: {url[:50]}...")
            except Exception as e:
                logger.error(f"Error generating presigned URL for S3 key {s3_key}: {str(e)}")
                # Create a direct S3 URL as fallback
                url = f"https://{os.getenv('S3_BUCKET_NAME', 'jmscpos-sandbox-chatbot-poc-bucket')}.s3.{os.getenv('AWS_REGION', 'us-east-1')}.amazonaws.com/{s3_key}"
                logger.warning(f"Using direct S3 URL as fallback: {url[:50]}...")

            # Add page number to URL if provided
            if page is not None and page > 0:
                # Add page fragment to URL
                if '#' not in url:
                    url = f"{url}#page={page}"
                logger.info(f"Added page {page} to document URL")

            return {
                "document_id": document_id,
                "filename": filename,
                "url": url,
                "expires_in": expiration,
                "page": page
            }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error generating document URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating document URL: {str(e)}"
        )

"""
Simple notification service for sending notifications to all users.
"""

import logging
from typing import List, Optional
from datetime import datetime

from app.database.db import get_db
from app.database.models import Notification, User

# Set up logging
logger = logging.getLogger(__name__)

def send_notification_to_all_users(
    title: str,
    message: str,
    notification_type: str,
    created_by: Optional[str] = None,
    document_id: Optional[str] = None,
    skip_user_ids: List[str] = None
) -> bool:
    """
    Send a notification to all users.

    Args:
        title: Short title for the notification
        message: Detailed message
        notification_type: Type of notification (document_added, document_deleted, etc.)
        created_by: ID of the user who triggered the notification
        document_id: ID of the related document (if any)
        skip_user_ids: List of user IDs to skip (e.g., the user who created the notification)

    Returns:
        True if successful, False otherwise
    """
    try:
        with get_db() as db:
            # Get all users
            users = db.query(User).all()

            # Skip specified users
            if skip_user_ids:
                users = [user for user in users if user.id not in skip_user_ids]

            logger.info(f"Creating notifications for {len(users)} users with title: {title}")

            # Create a notification for each user
            notification_ids = []
            for user in users:
                notification = Notification(
                    title=title,
                    message=message,
                    type=notification_type,
                    created_by=created_by,
                    user_id=user.id,
                    document_id=document_id,
                    is_global=False,
                    is_read=False,
                    created_at=datetime.now()
                )
                db.add(notification)
                db.flush()  # Flush to get the ID
                notification_ids.append(notification.id)
                logger.debug(f"Created notification {notification.id} for user {user.id}")

            # Create a global notification as well for backward compatibility
            global_notification = Notification(
                title=title,
                message=message,
                type=notification_type,
                created_by=created_by,
                user_id=None,  # No specific user
                document_id=document_id,
                is_global=True,
                is_read=False,
                created_at=datetime.now()
            )
            db.add(global_notification)

            db.commit()
            logger.info(f"Successfully created notifications for {len(users)} users. IDs: {notification_ids}")
            return True
    except Exception as e:
        logger.error(f"Error creating notifications: {str(e)}")
        return False

def send_notification_to_user(
    user_id: str,
    title: str,
    message: str,
    notification_type: str,
    created_by: Optional[str] = None,
    document_id: Optional[str] = None
) -> bool:
    """
    Send a notification to a specific user.

    Args:
        user_id: ID of the user to send the notification to
        title: Short title for the notification
        message: Detailed message
        notification_type: Type of notification (document_added, document_deleted, etc.)
        created_by: ID of the user who triggered the notification
        document_id: ID of the related document (if any)

    Returns:
        True if successful, False otherwise
    """
    try:
        with get_db() as db:
            notification = Notification(
                title=title,
                message=message,
                type=notification_type,
                created_by=created_by,
                user_id=user_id,
                document_id=document_id,
                is_global=False,
                is_read=False,
                created_at=datetime.now()
            )
            db.add(notification)
            db.commit()
            logger.info(f"Created notification for user {user_id}")
            return True
    except Exception as e:
        logger.error(f"Error creating notification for user {user_id}: {str(e)}")
        return False

def send_notification_to_group_members(
    group_id: str,
    title: str,
    message: str,
    notification_type: str,
    created_by: Optional[str] = None,
    document_id: Optional[str] = None,
    skip_user_ids: List[str] = None
) -> bool:
    """
    Send a notification to all members of a group.

    Args:
        group_id: ID of the group
        title: Short title for the notification
        message: Detailed message
        notification_type: Type of notification (document_added, document_deleted, etc.)
        created_by: ID of the user who triggered the notification
        document_id: ID of the related document (if any)
        skip_user_ids: List of user IDs to skip (e.g., the user who created the notification)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from app.auth import UserManager
        user_manager = UserManager()

        # Get all users in this group
        users_in_group = user_manager.get_group_members(group_id)

        # Skip specified users
        if skip_user_ids:
            users_in_group = [user_id for user_id in users_in_group if user_id not in skip_user_ids]

        with get_db() as db:
            # Create a notification for each user in the group
            for user_id in users_in_group:
                notification = Notification(
                    title=title,
                    message=message,
                    type=notification_type,
                    created_by=created_by,
                    user_id=user_id,
                    document_id=document_id,
                    is_global=False,
                    is_read=False,
                    created_at=datetime.now()
                )
                db.add(notification)

            db.commit()
            logger.info(f"Created notifications for {len(users_in_group)} users in group {group_id}")
            return True
    except Exception as e:
        logger.error(f"Error creating notifications for group {group_id}: {str(e)}")
        return False

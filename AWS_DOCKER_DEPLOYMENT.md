# AWS Docker Deployment - Google Cloud Authentication Fix

## Problem
You're getting Google Cloud authentication errors when deploying your Docker container to AWS because the local `google_credentials.json` file is not available in the AWS environment.

## Simple Solution: Environment Variable Method

### Step 1: Encode Your Google Credentials

On your local machine, encode your Google credentials file to base64:

```bash
# Encode your credentials file to base64 (single line, no wrapping)
base64 -w 0 google_credentials.json > google_credentials_base64.txt

# Copy the content of this file - you'll need it for the environment variable
cat google_credentials_base64.txt
```

### Step 2: Update Your AWS Docker Run Command

When you run your Docker container on AWS, add the `GOOGLE_CREDENTIALS_BASE64` environment variable:

```bash
docker run -d \
  -p 8000:8000 \
  -e ENV=production \
  -e GOOGLE_PROJECT_ID=big-buttress-454212-h9 \
  -e GOOGLE_CREDENTIALS_BASE64="<paste your base64 string here>" \
  -e DB_HOST=your-rds-endpoint \
  -e DB_USER=your-db-user \
  -e DB_PASSWORD=your-db-password \
  -e DB_NAME=internal_apps_db \
  -e S3_BUCKET_NAME=your-s3-bucket \
  -e AWS_ACCESS_KEY_ID=your-access-key \
  -e AWS_SECRET_ACCESS_KEY=your-secret-key \
  -e AWS_REGION=us-east-1 \
  your-image-name:latest
```

### Step 3: If Using ECS Task Definition

If you're using AWS ECS, add this to your task definition:

```json
{
  "environment": [
    {
      "name": "ENV",
      "value": "production"
    },
    {
      "name": "GOOGLE_PROJECT_ID",
      "value": "big-buttress-454212-h9"
    },
    {
      "name": "GOOGLE_CREDENTIALS_BASE64",
      "value": "<your base64 encoded credentials>"
    },
    {
      "name": "DB_HOST",
      "value": "your-rds-endpoint"
    },
    {
      "name": "DB_NAME",
      "value": "internal_apps_db"
    }
  ]
}
```

### Step 4: If Using AWS Elastic Beanstalk

Add these environment variables in your Elastic Beanstalk configuration:

```
ENV=production
GOOGLE_PROJECT_ID=big-buttress-454212-h9
GOOGLE_CREDENTIALS_BASE64=<your base64 encoded credentials>
DB_HOST=your-rds-endpoint
DB_NAME=internal_apps_db
```

## Alternative: AWS Secrets Manager (More Secure)

If you prefer a more secure approach:

### Step 1: Store Credentials in AWS Secrets Manager

```bash
aws secretsmanager create-secret \
  --name "google-service-account-credentials" \
  --description "Google Cloud service account credentials" \
  --secret-string file://google_credentials.json \
  --region us-east-1
```

### Step 2: Add IAM Permission

Make sure your AWS service (ECS task role, EC2 instance role, etc.) has this permission:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:us-east-1:*:secret:google-service-account-credentials-*"
    }
  ]
}
```

### Step 3: Update Docker Run Command

```bash
docker run -d \
  -p 8000:8000 \
  -e ENV=production \
  -e GOOGLE_PROJECT_ID=big-buttress-454212-h9 \
  -e AWS_REGION=us-east-1 \
  -e GOOGLE_CREDENTIALS_SECRET_NAME=google-service-account-credentials \
  -e DB_HOST=your-rds-endpoint \
  -e DB_NAME=internal_apps_db \
  your-image-name:latest
```

## Testing the Fix

After deployment, check your application logs. You should see:

```
INFO:app.api.app:Google Cloud credentials configured: /tmp/google_credentials_xyz.json
INFO:app.embedding.embedder:Using Google Vertex AI embeddings with model: text-embedding-004
```

## Troubleshooting

### If you still get authentication errors:

1. **Check the base64 encoding**: Make sure there are no line breaks in your base64 string
2. **Verify the environment variable**: Check that `GOOGLE_CREDENTIALS_BASE64` is properly set
3. **Check the Google Project ID**: Make sure `GOOGLE_PROJECT_ID=big-buttress-454212-h9` is correct
4. **Check application logs**: Look for credential setup messages

### Common Issues:

- **Line breaks in base64**: Use `base64 -w 0` to avoid line breaks
- **Missing environment variables**: Make sure all required variables are set
- **Wrong project ID**: Double-check your Google Cloud project ID
- **Invalid JSON**: Make sure your original credentials file is valid JSON

## Recommended Approach

For your AWS Docker deployment, I recommend:

1. **Use the Environment Variable method** (Step 1-2 above) for simplicity
2. **Store the base64 string securely** in your deployment system
3. **Consider AWS Secrets Manager** for production environments with multiple services

The application will automatically detect and use the credentials from the environment variable, creating a temporary file as needed.

## Security Note

The base64 encoding is just for transport - it's not encryption. For production:
- Use AWS Secrets Manager or AWS Systems Manager Parameter Store
- Ensure proper IAM permissions
- Rotate credentials regularly
- Monitor access logs

#!/usr/bin/env python3
"""
Test script to verify Google Cloud credentials setup for AWS deployment.
"""

import os
import sys
import json
import base64

def test_local_file():
    """Test if google_credentials.json exists locally"""
    print("=== Testing Local File ===")
    
    local_paths = [
        "google_credentials.json",
        "/app/google_credentials.json",
        "chatbot_support_backend/google_credentials.json"
    ]
    
    for path in local_paths:
        if os.path.exists(path):
            print(f"✅ Found credentials file: {path}")
            try:
                with open(path, 'r') as f:
                    creds = json.load(f)
                    print(f"   Project ID: {creds.get('project_id', 'Not found')}")
                    print(f"   Client Email: {creds.get('client_email', 'Not found')}")
                return path
            except Exception as e:
                print(f"❌ Error reading file {path}: {e}")
        else:
            print(f"❌ File not found: {path}")
    
    return None

def test_environment_variable():
    """Test if GOOGLE_CREDENTIALS_BASE64 environment variable is set"""
    print("\n=== Testing Environment Variable ===")
    
    google_creds_b64 = os.environ.get("GOOGLE_CREDENTIALS_BASE64")
    if not google_creds_b64:
        print("❌ GOOGLE_CREDENTIALS_BASE64 environment variable not set")
        return None
    
    print("✅ GOOGLE_CREDENTIALS_BASE64 environment variable is set")
    print(f"   Length: {len(google_creds_b64)} characters")
    
    try:
        # Try to decode it
        creds_json = base64.b64decode(google_creds_b64).decode('utf-8')
        creds_data = json.loads(creds_json)
        print(f"✅ Successfully decoded credentials")
        print(f"   Project ID: {creds_data.get('project_id', 'Not found')}")
        print(f"   Client Email: {creds_data.get('client_email', 'Not found')}")
        return creds_data
    except Exception as e:
        print(f"❌ Error decoding credentials: {e}")
        return None

def test_aws_environment():
    """Test if we're in AWS environment"""
    print("\n=== Testing AWS Environment ===")
    
    aws_region = os.environ.get("AWS_REGION")
    if aws_region:
        print(f"✅ AWS_REGION is set: {aws_region}")
    else:
        print("❌ AWS_REGION not set")
    
    # Check for other AWS indicators
    aws_indicators = [
        "AWS_EXECUTION_ENV",
        "AWS_LAMBDA_FUNCTION_NAME",
        "ECS_CONTAINER_METADATA_URI",
        "AWS_BATCH_JOB_ID"
    ]
    
    for indicator in aws_indicators:
        if os.environ.get(indicator):
            print(f"✅ AWS environment detected: {indicator}={os.environ.get(indicator)}")
            return True
    
    print("ℹ️  No AWS environment indicators found (might be running locally)")
    return False

def test_google_project_id():
    """Test if Google Project ID is set"""
    print("\n=== Testing Google Project ID ===")
    
    project_id = os.environ.get("GOOGLE_PROJECT_ID")
    if project_id:
        print(f"✅ GOOGLE_PROJECT_ID is set: {project_id}")
        return project_id
    else:
        print("❌ GOOGLE_PROJECT_ID environment variable not set")
        return None

def create_base64_credentials():
    """Helper to create base64 encoded credentials from local file"""
    print("\n=== Creating Base64 Credentials ===")
    
    local_file = test_local_file()
    if not local_file:
        print("❌ No local credentials file found to encode")
        return
    
    try:
        with open(local_file, 'rb') as f:
            file_content = f.read()
            base64_content = base64.b64encode(file_content).decode('utf-8')
        
        print(f"✅ Successfully encoded {local_file}")
        print(f"   Base64 length: {len(base64_content)} characters")
        print("\n📋 Copy this value for GOOGLE_CREDENTIALS_BASE64 environment variable:")
        print("=" * 80)
        print(base64_content)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Error encoding file: {e}")

def main():
    """Main test function"""
    print("🔍 Google Cloud Credentials Test for AWS Deployment")
    print("=" * 60)
    
    # Test local file
    local_file = test_local_file()
    
    # Test environment variable
    env_creds = test_environment_variable()
    
    # Test AWS environment
    is_aws = test_aws_environment()
    
    # Test Google Project ID
    project_id = test_google_project_id()
    
    # Summary
    print("\n=== Summary ===")
    
    if local_file:
        print("✅ Local credentials file found - good for local development")
    
    if env_creds:
        print("✅ Environment variable credentials found - good for AWS deployment")
    elif local_file and not env_creds:
        print("⚠️  Only local file found - you need to set GOOGLE_CREDENTIALS_BASE64 for AWS")
        create_base64_credentials()
    
    if project_id:
        print("✅ Google Project ID configured")
    else:
        print("❌ Google Project ID missing - set GOOGLE_PROJECT_ID environment variable")
    
    if is_aws and not env_creds:
        print("❌ Running in AWS but no environment credentials - authentication will fail")
    elif is_aws and env_creds:
        print("✅ Running in AWS with environment credentials - should work")
    
    print("\n🚀 Next steps for AWS deployment:")
    if not env_creds and local_file:
        print("1. Encode your credentials file to base64 (see output above)")
        print("2. Set GOOGLE_CREDENTIALS_BASE64 environment variable in your AWS deployment")
    if not project_id:
        print("3. Set GOOGLE_PROJECT_ID=big-buttress-454212-h9 in your AWS deployment")
    
    print("4. Redeploy your application")
    print("5. Check application logs for 'Google Cloud credentials configured' message")

if __name__ == "__main__":
    main()

# External ChromaDB Setup Guide

This guide explains how to configure the chatbot backend to use an external ChromaDB server for production deployments.

## Overview

By default, the application uses local ChromaDB with file persistence. For production environments, you can configure it to use an external ChromaDB server, which provides:

- Better scalability and performance
- Centralized vector storage
- High availability options
- Easier backup and maintenance

## Configuration

### Environment Variables

Add these environment variables to your `.env` file or deployment configuration:

```bash
# Enable external ChromaDB
USE_EXTERNAL_CHROMA=true

# ChromaDB server connection details
CHROMA_HOST=your-chromadb-host
CHROMA_PORT=8000
CHROMA_COLLECTION_NAME=chatbot_documents
```

### Local Development with External ChromaDB

1. **Start ChromaDB server using Docker Compose:**
   ```bash
   docker-compose -f docker-compose.chromadb.yml up -d
   ```

2. **Update your `.env` file:**
   ```bash
   USE_EXTERNAL_CHROMA=true
   CHROMA_HOST=localhost
   CHROMA_PORT=8000
   CHROMA_COLLECTION_NAME=chatbot_documents
   ```

3. **Start your application:**
   ```bash
   docker-compose up -d
   ```

### Production Deployment

#### Option 1: Separate ChromaDB Service

1. Deploy ChromaDB as a separate service using the provided `docker-compose.chromadb.yml`
2. Update your application environment variables to point to the ChromaDB service
3. Ensure network connectivity between your application and ChromaDB service

#### Option 2: Managed ChromaDB Service

1. Use a managed ChromaDB service (if available in your cloud provider)
2. Configure the connection details in your environment variables
3. Ensure proper authentication and network security

## Configuration Examples

### Docker Compose with External ChromaDB

```yaml
# In your main docker-compose.yml
services:
  backend:
    # ... other configuration
    environment:
      - USE_EXTERNAL_CHROMA=true
      - CHROMA_HOST=chromadb-server
      - CHROMA_PORT=8000
      - CHROMA_COLLECTION_NAME=chatbot_documents
    depends_on:
      - chromadb-server
    networks:
      - app-network

  chromadb-server:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    volumes:
      - chromadb-data:/chroma/chroma
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  chromadb-data:
    driver: local
```

### Kubernetes Deployment

```yaml
# ChromaDB Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chromadb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chromadb
  template:
    metadata:
      labels:
        app: chromadb
    spec:
      containers:
      - name: chromadb
        image: chromadb/chroma:latest
        ports:
        - containerPort: 8000
        env:
        - name: CHROMA_SERVER_HOST
          value: "0.0.0.0"
        - name: CHROMA_SERVER_HTTP_PORT
          value: "8000"
        - name: IS_PERSISTENT
          value: "TRUE"
        volumeMounts:
        - name: chromadb-storage
          mountPath: /chroma/chroma
      volumes:
      - name: chromadb-storage
        persistentVolumeClaim:
          claimName: chromadb-pvc

---
# ChromaDB Service
apiVersion: v1
kind: Service
metadata:
  name: chromadb-service
spec:
  selector:
    app: chromadb
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
```

## Migration from Local to External ChromaDB

1. **Backup existing data** (if using local ChromaDB with persistence)
2. **Deploy external ChromaDB server**
3. **Update environment variables** to use external ChromaDB
4. **Restart application** - it will automatically connect to the external server
5. **Re-upload documents** or migrate existing vector data if needed

## Troubleshooting

### Connection Issues

- Verify ChromaDB server is running and accessible
- Check network connectivity between application and ChromaDB
- Ensure correct host and port configuration

### Performance Issues

- Monitor ChromaDB server resources (CPU, memory, disk)
- Consider scaling ChromaDB horizontally if supported
- Optimize collection configuration for your use case

### Data Persistence

- Ensure ChromaDB server has persistent storage configured
- Regular backups of ChromaDB data directory
- Test disaster recovery procedures

## Security Considerations

- Use authentication if supported by your ChromaDB deployment
- Implement network security (VPC, security groups, etc.)
- Encrypt data in transit and at rest
- Regular security updates for ChromaDB server

## Monitoring

- Monitor ChromaDB server health and performance
- Set up alerts for connection failures
- Track vector store operations and response times
- Monitor storage usage and growth

For more information about ChromaDB, visit: https://docs.trychroma.com/

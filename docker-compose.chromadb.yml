version: '3.8'

services:
  # ChromaDB service for external vector storage
  chromadb:
    image: chromadb/chroma:latest
    restart: always
    ports:
      - "8001:8000"  # Map to port 8001 to match your .env configuration
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - IS_PERSISTENT=TRUE
      - PERSIST_DIRECTORY=/chroma/chroma
      - ANONYMIZED_TELEMETRY=FALSE
    volumes:
      - chromadb-data:/chroma/chroma
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - chromadb-network

volumes:
  chromadb-data:
    driver: local

networks:
  chromadb-network:
    driver: bridge

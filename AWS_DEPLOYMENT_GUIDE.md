# AWS Deployment Guide - Google Cloud Authentication Fix

## Problem
When deploying to AWS, you're getting a Google Cloud authentication error because the local `google_credentials.json` file is not available in the AWS environment.

## Solutions

### Option 1: AWS Secrets Manager (Recommended for Production)

#### Step 1: Store Google Credentials in AWS Secrets Manager

```bash
# Store your Google service account credentials in AWS Secrets Manager
aws secretsmanager create-secret \
  --name "google-service-account-credentials" \
  --description "Google Cloud service account credentials for chatbot" \
  --secret-string file://google_credentials.json \
  --region us-east-1
```

#### Step 2: Update Environment Variables

Add these environment variables to your AWS deployment:

```bash
# Required for AWS Secrets Manager
AWS_REGION=us-east-1
GOOGLE_CREDENTIALS_SECRET_NAME=google-service-account-credentials

# Your existing Google Project ID
GOOGLE_PROJECT_ID=big-buttress-454212-h9
```

#### Step 3: Update IAM Permissions

Make sure your AWS service (ECS task, EC2 instance, etc.) has permission to access Secrets Manager:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:us-east-1:YOUR-ACCOUNT-ID:secret:google-service-account-credentials-*"
    }
  ]
}
```

### Option 2: Environment Variable (Quick Fix)

If you prefer not to use Secrets Manager, you can encode your credentials as a base64 string:

#### Step 1: Encode Credentials
```bash
# Encode your credentials file to base64
base64 -w 0 google_credentials.json > google_credentials_base64.txt
```

#### Step 2: Set Environment Variable
```bash
GOOGLE_CREDENTIALS_BASE64="<paste the base64 string here>"
```

#### Step 3: Update Application Code
Add this to your application startup (in `app/api/app.py`):

```python
import base64
import json
import tempfile

# Decode Google credentials from environment variable
google_creds_b64 = os.environ.get("GOOGLE_CREDENTIALS_BASE64")
if google_creds_b64:
    try:
        # Decode base64 credentials
        creds_json = base64.b64decode(google_creds_b64).decode('utf-8')
        creds_data = json.loads(creds_json)
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(creds_data, temp_file, indent=2)
        temp_file.close()
        
        # Set environment variable
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temp_file.name
        logger.info(f"Google credentials loaded from environment variable")
    except Exception as e:
        logger.error(f"Error loading Google credentials from environment: {str(e)}")
```

### Option 3: AWS Parameter Store

#### Step 1: Store Credentials in Parameter Store
```bash
aws ssm put-parameter \
  --name "/chatbot/google-credentials" \
  --value file://google_credentials.json \
  --type "SecureString" \
  --region us-east-1
```

#### Step 2: Update IAM Permissions
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter"
      ],
      "Resource": "arn:aws:ssm:us-east-1:YOUR-ACCOUNT-ID:parameter/chatbot/google-credentials"
    }
  ]
}
```

## Testing the Fix

After implementing any of the above solutions:

1. **Deploy your application** to AWS
2. **Check the logs** for Google credentials setup messages
3. **Try uploading a document** to test embedding generation
4. **Verify** that you see logs like:
   ```
   INFO:app.api.app:Google Cloud credentials configured: /tmp/google_credentials_xyz.json
   INFO:app.embedding.embedder:Using Google Vertex AI embeddings with model: text-embedding-004
   ```

## Troubleshooting

### If you still get authentication errors:

1. **Check AWS IAM permissions** - Make sure your service has the required permissions
2. **Verify the secret exists** in AWS Secrets Manager:
   ```bash
   aws secretsmanager describe-secret --secret-id google-service-account-credentials
   ```
3. **Check the region** - Make sure you're using the correct AWS region
4. **Verify Google Project ID** - Make sure `GOOGLE_PROJECT_ID` is set correctly

### Common Issues:

- **Wrong AWS region**: Make sure `AWS_REGION` matches where you stored the secret
- **Missing IAM permissions**: The service needs `secretsmanager:GetSecretValue` permission
- **Invalid JSON**: Make sure your Google credentials file is valid JSON
- **Wrong project ID**: Verify your Google Cloud project ID is correct

## Recommended Approach

For production deployments, use **Option 1 (AWS Secrets Manager)** because:
- ✅ More secure than environment variables
- ✅ Centralized credential management
- ✅ Automatic rotation support
- ✅ Audit logging
- ✅ Fine-grained access control

The application code has been updated to automatically try multiple methods:
1. Check existing `GOOGLE_APPLICATION_CREDENTIALS`
2. Check local file (`/app/google_credentials.json`)
3. Retrieve from AWS Secrets Manager
4. Fall back gracefully if none available

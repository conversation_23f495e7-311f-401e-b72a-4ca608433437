#!/usr/bin/env python3
"""
Test script to verify that the ExternalChromaVectorStore wrapper works correctly.
This test simulates the external ChromaDB scenario with a mock server.
"""

import os
import sys
import logging
from pathlib import Path
from unittest.mock import Mock, patch

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "chatbot_support_backend"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_external_chroma_wrapper():
    """Test that ExternalChromaVectorStore wrapper provides correct interface."""
    
    # Set environment variables for external ChromaDB
    os.environ["USE_EXTERNAL_CHROMA"] = "true"
    os.environ["CHROMA_HOST"] = "localhost"
    os.environ["CHROMA_PORT"] = "8001"
    os.environ["CHROMA_COLLECTION_NAME"] = "test_collection"
    
    try:
        logger.info("Testing ExternalChromaVectorStore wrapper...")
        
        # Import required classes
        from app.embedding.vector_store import ExternalChromaClient, ExternalChromaVectorStore
        from langchain.schema.document import Document
        
        # Create a mock embeddings object
        mock_embeddings = Mock()
        mock_embeddings.embed_query.return_value = [0.1, 0.2, 0.3, 0.4]
        
        # Create a mock client
        mock_client = Mock(spec=ExternalChromaClient)
        mock_client.query.return_value = {
            'documents': [['Test document content']],
            'metadatas': [[{'source': 'test.pdf'}]],
            'distances': [[0.1]]
        }
        mock_client.add_documents.return_value = True
        
        # Create the wrapper
        wrapper = ExternalChromaVectorStore(
            client=mock_client,
            collection_name="test_collection",
            embeddings=mock_embeddings
        )
        
        # Test similarity_search method
        logger.info("Testing similarity_search method...")
        results = wrapper.similarity_search("test query", k=1)
        assert len(results) == 1
        assert isinstance(results[0], Document)
        assert results[0].page_content == "Test document content"
        assert results[0].metadata == {'source': 'test.pdf'}
        logger.info("✅ similarity_search works correctly")
        
        # Test similarity_search_with_score method
        logger.info("Testing similarity_search_with_score method...")
        results_with_scores = wrapper.similarity_search_with_score("test query", k=1)
        assert len(results_with_scores) == 1
        doc, score = results_with_scores[0]
        assert isinstance(doc, Document)
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        logger.info("✅ similarity_search_with_score works correctly")
        
        # Test as_retriever method
        logger.info("Testing as_retriever method...")
        retriever = wrapper.as_retriever()
        assert hasattr(retriever, '_get_relevant_documents')
        
        # Test the retriever
        retriever_results = retriever._get_relevant_documents("test query")
        assert len(retriever_results) == 1
        assert isinstance(retriever_results[0], Document)
        logger.info("✅ as_retriever works correctly")
        
        # Test add_documents method
        logger.info("Testing add_documents method...")
        test_docs = [Document(page_content="Test content", metadata={"test": "meta"})]
        result = wrapper.add_documents(test_docs)
        assert result is True
        mock_client.add_documents.assert_called_once()
        logger.info("✅ add_documents works correctly")
        
        logger.info("🎉 All ExternalChromaVectorStore wrapper tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_store_initialization():
    """Test that VectorStore initializes correctly with external ChromaDB."""
    
    # Set environment variables for external ChromaDB
    os.environ["USE_EXTERNAL_CHROMA"] = "true"
    os.environ["CHROMA_HOST"] = "localhost"
    os.environ["CHROMA_PORT"] = "8001"
    os.environ["CHROMA_COLLECTION_NAME"] = "test_collection"
    
    try:
        logger.info("Testing VectorStore initialization with external ChromaDB...")
        
        # Mock the heartbeat to return True (connection successful)
        with patch('app.embedding.vector_store.ExternalChromaClient.heartbeat', return_value=True):
            from app.embedding.vector_store import VectorStore
            
            # Create a mock embeddings object
            mock_embeddings = Mock()
            mock_embeddings.embed_query.return_value = [0.1, 0.2, 0.3, 0.4]
            
            # Create VectorStore instance
            vector_store = VectorStore(
                embeddings=mock_embeddings,
                store_type="chroma",
                user_id="test_user"
            )
            
            # Check that it's configured for external ChromaDB
            assert vector_store.is_external_chroma is True
            assert vector_store.chroma_client is not None
            assert vector_store.collection_name == "test_collection"
            
            logger.info("✅ VectorStore initializes correctly with external ChromaDB")
            return True
            
    except Exception as e:
        logger.error(f"❌ VectorStore initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("Starting ExternalChromaVectorStore wrapper tests...")
    
    # Test the wrapper functionality
    logger.info("\n" + "="*60)
    logger.info("Testing ExternalChromaVectorStore wrapper")
    logger.info("="*60)
    wrapper_success = test_external_chroma_wrapper()
    
    # Test VectorStore initialization
    logger.info("\n" + "="*60)
    logger.info("Testing VectorStore initialization")
    logger.info("="*60)
    init_success = test_vector_store_initialization()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"ExternalChromaVectorStore wrapper: {'✅ PASS' if wrapper_success else '❌ FAIL'}")
    logger.info(f"VectorStore initialization: {'✅ PASS' if init_success else '❌ FAIL'}")
    
    if wrapper_success and init_success:
        logger.info("🎉 All tests passed! The external ChromaDB wrapper works correctly.")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed.")
        sys.exit(1)
